import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'
import websocketService from '@/services/websocket'
import echo from '@/services/echo'
import { useAuthStore } from '@/stores/auth'

interface CoinflipCreateRequest {
  bet: number
  side: 'heads' | 'tails'
  currency_id?: number
}

interface CoinflipGame {
  id: number
  creator: {
    id: number
    username: string
  } | null
  joiner: {
    id: number
    username: string
  } | null
  winner: {
    id: number
    username: string
  } | null
  amount: number
  amount_formatted: string
  creator_side: 'heads' | 'tails'
  joiner_side: 'heads' | 'tails' | null
  result: 'heads' | 'tails' | null
  status: 'waiting' | 'completed' | 'cancelled'
  currency_code: string
  currency_symbol: string
  reference_id: string
  created_at: string
  updated_at: string | null
}

interface CoinflipConfig {
  house_edge: number
  sides: string[]
}

interface CoinflipMinimumBetResult {
  minimum_bet: number
  minimum_bet_formatted: string
  currency_code: string
  currency_symbol: string
}

export const useCoinflipStore = defineStore('coinflip', () => {
  // Configure axios for Sanctum cookie authentication
  axios.defaults.withCredentials = true
  axios.defaults.withXSRFToken = true

  const waitingGames = ref<CoinflipGame[]>([])
  const myGames = ref<CoinflipGame[]>([])
  const lastResult = ref<CoinflipGame | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const minimumBetInfo = ref<CoinflipMinimumBetResult | null>(null)
  const config = ref<CoinflipConfig | null>(null)

  // Statistics
  const totalGames = ref(0)
  const totalWins = ref(0)
  const totalProfit = ref(0)

  const winRate = computed(() => {
    if (totalGames.value === 0) return 0
    return (totalWins.value / totalGames.value) * 100
  })

  // WebSocket callback references for proper cleanup
  const gameCreatedCallback = (data: CoinflipGame) => {
    console.log('Coinflip game created via WebSocket:', data)

    // Add to waiting games if not already present
    const existingGame = waitingGames.value.find(game => game.id === data.id)
    if (!existingGame) {
      waitingGames.value.unshift(data)
    }
  }

  const gameJoinedCallback = (data: CoinflipGame) => {
    console.log('Coinflip game joined via WebSocket:', data)

    // Remove from waiting games
    waitingGames.value = waitingGames.value.filter(game => game.id !== data.id)

    // Update last result if this is our game
    const authStore = useAuthStore()
    if (authStore.user && (data.creator?.id === authStore.user.id || data.joiner?.id === authStore.user.id)) {
      lastResult.value = data

      // Update statistics if we were involved
      totalGames.value++
      if (data.winner?.id === authStore.user.id) {
        totalWins.value++
        // Calculate profit (winner gets 91% of total pot minus their original bet)
        const totalPot = data.amount * 2
        const winnerProfit = totalPot * 0.91 - data.amount
        totalProfit.value += winnerProfit
      } else {
        // Lost our bet
        totalProfit.value -= data.amount
      }

      // Trigger animation for the game creator when someone joins
      // This will be handled by the component that listens to this store
      // We'll emit a custom event that the component can listen to
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('coinflip-game-resolved', {
          detail: { gameId: data.id, result: data.result }
        }))
      }
    }

    // Update my games list
    const existingGameIndex = myGames.value.findIndex(game => game.id === data.id)
    if (existingGameIndex >= 0) {
      myGames.value[existingGameIndex] = data
    } else {
      myGames.value.unshift(data)
    }
  }

  /**
   * Create a new coinflip game
   */
  const createGame = async (request: CoinflipCreateRequest): Promise<CoinflipGame> => {
    isLoading.value = true
    error.value = null

    try {
      const response = await axios.post(`${import.meta.env.VITE_API_URL}/api/coinflip/create`, request)

      if (response.data.success) {
        const result = response.data.data as CoinflipGame

        // Add to my games immediately
        myGames.value.unshift(result)

        return result
      } else {
        throw new Error(response.data.error || 'Failed to create game')
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || err.message || 'Failed to create game'
      error.value = errorMessage
      throw new Error(errorMessage)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Join an existing coinflip game
   */
  const joinGame = async (gameId: number): Promise<CoinflipGame> => {
    isLoading.value = true
    error.value = null

    try {
      const response = await axios.post(`${import.meta.env.VITE_API_URL}/api/coinflip/${gameId}/join`)

      if (response.data.success) {
        const result = response.data.data as CoinflipGame

        // Update last result
        lastResult.value = result

        return result
      } else {
        throw new Error(response.data.error || 'Failed to join game')
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || err.message || 'Failed to join game'
      error.value = errorMessage
      throw new Error(errorMessage)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Join a game with a bot
   */
  const joinWithBot = async (gameId: number): Promise<CoinflipGame> => {
    isLoading.value = true
    error.value = null

    try {
      const response = await axios.post(`${import.meta.env.VITE_API_URL}/api/coinflip/${gameId}/bot`)

      if (response.data.success) {
        const result = response.data.data as CoinflipGame

        // Update last result
        lastResult.value = result

        return result
      } else {
        throw new Error(response.data.error || 'Failed to join with bot')
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || err.message || 'Failed to join with bot'
      error.value = errorMessage
      throw new Error(errorMessage)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Fetch waiting games for lobby
   */
  const fetchLobby = async (currencyId?: number): Promise<void> => {
    try {
      const params = currencyId ? { currency_id: currencyId } : {}
      const response = await axios.get(`${import.meta.env.VITE_API_URL}/api/coinflip/lobby`, { params })

      if (response.data.success) {
        waitingGames.value = response.data.data
      }
    } catch (err: any) {
      console.error('Failed to fetch lobby:', err)
      error.value = 'Failed to fetch lobby games'
    }
  }

  /**
   * Fetch user's games
   */
  const fetchMyGames = async (currencyId?: number): Promise<void> => {
    try {
      const params = currencyId ? { currency_id: currencyId } : {}
      const response = await axios.get(`${import.meta.env.VITE_API_URL}/api/coinflip/my-games`, { params })

      if (response.data.success) {
        myGames.value = response.data.data
      }
    } catch (err: any) {
      console.error('Failed to fetch my games:', err)
      error.value = 'Failed to fetch your games'
    }
  }

  /**
   * Get minimum bet for a currency
   */
  const getMinimumBet = async (currencyId: number): Promise<CoinflipMinimumBetResult> => {
    try {
      const response = await axios.post(`${import.meta.env.VITE_API_URL}/api/coinflip/minimum-bet`, {
        currency_id: currencyId
      })

      if (response.data.success) {
        minimumBetInfo.value = response.data.data
        return response.data.data
      } else {
        throw new Error(response.data.error || 'Failed to get minimum bet')
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || err.message || 'Failed to get minimum bet'
      error.value = errorMessage
      throw new Error(errorMessage)
    }
  }

  /**
   * Get coinflip game configuration
   */
  const getConfig = async (): Promise<CoinflipConfig> => {
    try {
      const response = await axios.get(`${import.meta.env.VITE_API_URL}/api/coinflip/config`)

      if (response.data.success) {
        config.value = response.data.data
        return response.data.data
      } else {
        throw new Error(response.data.error || 'Failed to get config')
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || err.message || 'Failed to get config'
      error.value = errorMessage
      throw new Error(errorMessage)
    }
  }

  /**
   * Clear error
   */
  const clearError = () => {
    error.value = null
  }

  /**
   * Initialize WebSocket connections
   */
  const initializeWebSocket = () => {
    const authStore = useAuthStore()

    if (!authStore.isAuthenticated || !authStore.user) {
      return
    }

    try {
      // Subscribe to lobby updates (public channel)
      echo.channel('coinflip.lobby')
        .listen('.game.created', gameCreatedCallback)
        .listen('.game.joined', gameJoinedCallback)

      // Subscribe to private user channel for personal game updates
      websocketService.subscribe('game.joined', gameJoinedCallback)

      console.log('Coinflip store: WebSocket initialized')
    } catch (error) {
      console.error('Failed to initialize coinflip WebSocket:', error)
    }
  }

  /**
   * Clean up WebSocket connections
   */
  const cleanupWebSocket = () => {
    try {
      // Leave lobby channel
      echo.leaveChannel('coinflip.lobby')

      // Unsubscribe from private channel
      websocketService.unsubscribe('game.joined', gameJoinedCallback)

      console.log('Coinflip store: WebSocket cleaned up')
    } catch (error) {
      console.error('Failed to cleanup coinflip WebSocket:', error)
    }
  }

  /**
   * Initialize the store
   */
  const initialize = () => {
    const authStore = useAuthStore()

    if (!authStore.isAuthenticated) {
      cleanupWebSocket()
      return
    }

    initializeWebSocket()
    fetchLobby()
    fetchMyGames()
  }

  /**
   * Reset store state
   */
  const reset = () => {
    waitingGames.value = []
    myGames.value = []
    lastResult.value = null
    isLoading.value = false
    error.value = null
    minimumBetInfo.value = null
    config.value = null
    totalGames.value = 0
    totalWins.value = 0
    totalProfit.value = 0
    cleanupWebSocket()
  }

  return {
    // State
    waitingGames,
    myGames,
    lastResult,
    isLoading,
    error,
    minimumBetInfo,
    config,
    totalGames,
    totalWins,
    totalProfit,

    // Computed
    winRate,

    // Actions
    createGame,
    joinGame,
    joinWithBot,
    fetchLobby,
    fetchMyGames,
    getMinimumBet,
    getConfig,
    clearError,
    initialize,
    reset,
    cleanupWebSocket,
  }
})
